import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../providers/subscription_provider.dart';
import '../../../../core/providers/auth_providers.dart' as core_auth;
import 'dart:developer' as dev;

/// Screen shown to existing subscribers who reinstall the app
class ExistingSubscriberScreen extends ConsumerStatefulWidget {
  const ExistingSubscriberScreen({super.key});

  @override
  ConsumerState<ExistingSubscriberScreen> createState() =>
      _ExistingSubscriberScreenState();
}

class _ExistingSubscriberScreenState
    extends ConsumerState<ExistingSubscriberScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    dev.log('ExistingSubscriberScreen: Initialized for existing subscriber');
  }

  @override
  Widget build(BuildContext context) {
    final subscriptionState = ref.watch(subscriptionProvider);

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              context.containerBackgroundColor,
              context.secondaryAccentColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Welcome back icon
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: context.accentColor.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.verified_user,
                        size: 60,
                        color: context.accentColor,
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Welcome back title
                    Text(
                      'Welcome Back!',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: context.primaryTextColor,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // Subscription status
                    Text(
                      _getSubscriptionStatusText(subscriptionState),
                      style: TextStyle(
                        fontSize: 16,
                        color: context.secondaryTextColor,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 32),

                    // Subscription details card
                    if (subscriptionState.subscription != null)
                      _buildSubscriptionCard(context, subscriptionState),

                    const SizedBox(height: 32),

                    // Benefits reminder
                    _buildBenefitsReminder(context),
                  ],
                ),
              ),

              // Continue button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _continueToApp,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.accentColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : const Text(
                          'Continue to App',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                ),
              ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getSubscriptionStatusText(SubscriptionState subscriptionState) {
    if (subscriptionState.subscription?.isInTrialPeriod() == true) {
      final daysLeft = subscriptionState.subscription!.expiryDate!
          .difference(DateTime.now())
          .inDays;
      return 'Your free trial is active with $daysLeft days remaining.';
    } else if (subscriptionState.hasActiveSubscription) {
      return 'Your premium subscription is active and ready to use.';
    } else {
      return 'We\'ve restored your account. Welcome back!';
    }
  }

  Widget _buildSubscriptionCard(
      BuildContext context, SubscriptionState subscriptionState) {
    final subscription = subscriptionState.subscription!;
    final isTrialActive = subscription.isInTrialPeriod();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isTrialActive ? Colors.orange : context.accentColor,
          width: 2,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                isTrialActive ? Icons.schedule : Icons.star,
                color: isTrialActive ? Colors.orange : context.accentColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  isTrialActive ? 'Free Trial' : 'Premium Subscription',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: context.primaryTextColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (subscription.expiryDate != null) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  isTrialActive ? 'Trial ends:' : 'Renews:',
                  style: TextStyle(
                    color: context.secondaryTextColor,
                    fontSize: 14,
                  ),
                ),
                Text(
                  _formatDate(subscription.expiryDate!),
                  style: TextStyle(
                    color: context.primaryTextColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBenefitsReminder(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.accentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            'Your Premium Benefits',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: context.accentColor,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Unlimited vehicle tracking',
                  style: TextStyle(
                    color: context.secondaryTextColor,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: context.accentColor,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Advanced maintenance reminders',
                  style: TextStyle(
                    color: context.secondaryTextColor,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: context.accentColor,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Detailed analytics and reports',
                  style: TextStyle(
                    color: context.secondaryTextColor,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _continueToApp() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Reset new signup flag since user has made their choice
      ref.read(core_auth.isNewSignupProvider.notifier).state = false;

      // Mark welcome subscription as shown using the notifier
      await ref
          .read(core_auth.welcomeSubscriptionShownProvider.notifier)
          .markAsShown();

      dev.log('ExistingSubscriberScreen: User continuing to main app');

      if (mounted) {
        // Force a small delay to ensure state updates are processed
        await Future.delayed(const Duration(milliseconds: 100));
        context.go('/main');
      }
    } catch (e) {
      dev.log('ExistingSubscriberScreen: Error continuing to app: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
